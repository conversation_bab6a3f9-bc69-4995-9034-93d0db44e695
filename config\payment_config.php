<?php
/**
 * 支付配置统一管理文件
 * 将WatchPay和JayaPay的关键配置项从数据库配置中提取到配置文件
 */

return [
    // 全局配置
    'global' => [
        'timeout' => 30,                    // HTTP请求超时时间（秒）
        'retry_times' => 3,                 // 重试次数
        'log_enabled' => true,              // 是否启用日志
        'log_level' => 'info',              // 日志级别
        'default_notify_domain' => 'https://www.lotteup.com', // 正式环境回调域名
        'unified_recharge_callback_url' => 'https://www.lotteup.com/api/transaction/unifiedCallback', // 统一充值回调地址
        'unified_withdrawal_callback_url' => 'https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback', // 统一代付回调地址
    ],

    // WatchPay配置
    'watch_pay' => [
        'enabled' => true,
        'default_gateway' => 'https://api.watchglb.com',

        // API接口地址配置
        'api_urls' => [
            'pay' => 'https://api.watchglb.com/pay/web',           // 充值地址
            'transfer' => 'https://api.watchglb.com/pay/transfer', // 代付提款地址
            'query_transfer' => 'https://api.watchglb.com/query/transfer', // 代付查询
            'query_balance' => 'https://api.watchglb.com/query/balance',   // 余额查询
        ],

        // 国家配置（目前只支持印尼）
        'countries' => [
            'ID' => [
                'name' => '印尼',
                'name_en' => 'Indonesia',
                'currency' => 'IDR',
                // 'merchant_id' => '200999218', 正式账户
                'merchant_id'=>'222888001',
                // 'pay_key' => 'YINGPTEMYQ7BAKOSHCAYZESK1WKU8XMK',        // 支付密钥 正式账户
                'pay_key'=> '6QUOUSXE6BCZPW8KZ1LQF7XZARXE69XO',
                // 'withdrawal_key' => 'DLVPXKVOMNOFVEN2AGEQWQUASPN2EPWQ',   // 代付密钥 正式账户
                'withdrawal_key'=> 'F67KSR2APPUJJVHSYAW8SSKAIGZMPWUE',
                'gateway_url' => 'https://api.watchglb.com',
                'notify_domain' => 'https://www.lotteup.com',
                'min_amount' => 20000,      // WatchPay最小金额：20,000 IDR
                'max_amount' => ********,   // WatchPay最大金额：50,000,000 IDR
                'enabled' => true,
                'pay_types' => [
                    // 只启用需要的支付方式
                    '220' => ['name' => '网银B2C二类', 'type' => 'online', 'enabled' => true, 'fee_rate' => 0.045, 'requires_bank_code' => true, 'min_amount' => 10000, 'max_amount' => ********],
                    '223' => ['name' => 'QRIS扫码二类', 'type' => 'scan', 'enabled' => true, 'fee_rate' => 0.055, 'requires_bank_code' => false, 'min_amount' => 10000, 'max_amount' => ********],

                    // 禁用其他支付方式
                    '200' => ['name' => '网银B2C一类', 'type' => 'online', 'enabled' => false, 'requires_bank_code' => true],
                    '201' => ['name' => '便利店一类', 'type' => 'offline', 'enabled' => false, 'requires_bank_code' => false],
                    '202' => ['name' => 'OVO钱包一类', 'type' => 'wallet', 'enabled' => false, 'requires_bank_code' => false],
                    '203' => ['name' => 'QRIS扫码一类', 'type' => 'scan', 'enabled' => false, 'requires_bank_code' => false],
                    '240' => ['name' => '网银B2C三类', 'type' => 'online', 'enabled' => false, 'requires_bank_code' => true],
                    '243' => ['name' => 'QRIS扫码三类', 'type' => 'scan', 'enabled' => false, 'requires_bank_code' => false],
                ],
                // WatchPay支持的银行编码（用于代付）
                'supported_banks' => [
                    'BCA' => ['name' => 'Bank Central Asia', 'name_id' => 'Bank Central Asia', 'enabled' => true],
                    'MANDIRI' => ['name' => 'Bank Mandiri', 'name_id' => 'Bank Mandiri', 'enabled' => true],
                    'BNI' => ['name' => 'Bank Negara Indonesia', 'name_id' => 'Bank Negara Indonesia', 'enabled' => true],
                    'BRI' => ['name' => 'Bank Rakyat Indonesia', 'name_id' => 'Bank Rakyat Indonesia', 'enabled' => true],
                    'PERMATA' => ['name' => 'Bank Permata', 'name_id' => 'Bank Permata', 'enabled' => true],
                    'CIMB' => ['name' => 'Bank CIMB Niaga', 'name_id' => 'Bank CIMB Niaga', 'enabled' => true],
                    'MAYBANK' => ['name' => 'Bank Maybank', 'name_id' => 'Bank Maybank', 'enabled' => true],
                    'DANAMON' => ['name' => 'Bank Danamon', 'name_id' => 'Bank Danamon', 'enabled' => true],
                    'BSI' => ['name' => 'Bank Syariah Indonesia', 'name_id' => 'Bank Syariah Indonesia', 'enabled' => true],
                    'BNC' => ['name' => 'Neo Commerce', 'name_id' => 'Bank Yudha Bhakti', 'enabled' => true],
                ]
            ],
        ],
        
        // 回调IP白名单
        'notify_ips' => [
            '*************'
        ],
    ],

    // JayaPay配置
    'jaya_pay' => [
        'enabled' => true,

        // 基础配置
        'gateway_urls' => [
            'cash_in' => 'https://openapi.jayapayment.com/gateway/prepaidOrder',  // 法币代收（收银台模式）
            'cash_in_api' => 'https://openapi.jayapayment.com/gateway/pay',       // 法币代收（API模式）
            'cash_out' => 'https://openapi.jayapayment.com/gateway/cash',         // 法币代付
            'query_order' => 'https://openapi.jayapayment.com/gateway/query',     // 订单查询
            'query_balance' => 'https://openapi.jayapayment.com/gateway/balance', // 余额查询
            'query_bank' => 'https://openapi.jayapayment.com/gateway/bankList',   // 银行列表查询
        ],

        // 商户后台地址
        'merchant_backend' => 'https://merchant.jayapayment.com/',

        // RSA密钥对生成地址
        'rsa_generator' => 'http://pay.hehebo.com:15082/index-rsa.jsp',

        // 平台公钥（用于验证回调签名）
        'platform_public_key' => '-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC2JoMfFqLsSJjAiCahEnlP3aRj
8yCT+WHzR+VvPBTw9S1i7iYWb+MY09CG/HYuHF4+IxshXDJygmndxKf/esuwPybS
8mAd//yubHpmZsmBqg1FffT8VH1APa6ZRWASUp4U01ZrbCCp35QA8FuWrJGMJxGx
4xk7KUtV2yujxC8noQIDAQAB
-----END PUBLIC KEY-----',

        // 商户配置
        'merchants' => [
            'default' => [
                merchant_code => 'S820250727142109000064',
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
                '
                public_key => '-----BEGIN PUBLIC KEY-----
                MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDTvbV9k5ak3y4efRutvJUBrgXsyYgdZia1ao/xOjm5WHRvWTMYFuFZLu17NhFQ+4HrW9vOSYea8hDmzv5Cv2mtuXfALz6oWSVxMsbNnDEKnDkjSYyte84+Oybm1Ldgig64EgTegp44nTeVnW9Bq2A9mB5D7UxohRj+WeDTWDGQNQIDAQAB
                -----END PUBLIC KEY-----'
                // 'merchant_code' => 'S820250727142109000064', 
//                 'private_key' => '-----BEGIN PRIVATE KEY-----
// MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKYHGBlNaigtV/On
// LkF+lRjGPGfu39ecUqvsPwt5alfk3QA+ZSddJRGrh2sPHewbg0rxkwSjvz6DOVyD
// NoFImhra5dCertyCup+fSKDasPE0EHAvN0GMyYCKIIv9fkERnm4r9FFNFuYogfva
// sZdjcCkx9ufWn4zPe6YJNxl2/lAlAgMBAAECgYAK9/+D1m07w8kigApKiewcQ6aE
// gE/aDQoaMSS2I5qevXBLXpPsvpbfHLu42zoRGf05041wuke9f8pnngSZFHyXy9oc
// rtdv6k+9zbI+p+g7yOXSVhv4P+pR1JThCTdGoKVu2rPdJizpgIWEJXIu5BQ69ia8
// yCOEkGpXkfhEvw87eQJBANVhhLTLxdnsBOZZDZtiC1lCnYfwRmAzHqj+hIW7YQcK
// +n44lPd3nR2IX2fCWjvAf2PvigkVu+1omJqfxsRQZVkCQQDHMFX3vrYA6Pe7Iqsr
// Y6tTwRyb78OeE0kveh6jt7Wq4JTa4LZ+U6NcibwzpoJNTD2n6RGCIp1YGFhXOy1t
// 5wutAkEAuqFtudhU95L4giS3wwURGgQq1qAwXam4kmTysik+5zMR/t4sZPoIwLDr
// iUwzMfnfMiBsIiARW8fVWcCP0FrlGQJAJyMzMQuS3VjpHCbytQLPbhR6l5UjvZKS
// hbacAJUfJ+Uaumr+0JGEfrE9fnSQE7oQP0bHPueoPOADs5gYFAkavQJAO+t4O8r1
// +F1XzLvpaQarIeTpd6eR0zVpr0CIvowe5k27VHuNaqQq7GHC93EN1j5GRs19vMJV
// Nl6mcI2n/saHlw==
// -----END PRIVATE KEY-----',
//                 'public_key' => '-----BEGIN PUBLIC KEY-----
// MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCmBxgZTWooLVfzpy5BfpUYxjxn
// 7t/XnFKr7D8LeWpX5N0APmUnXSURq4drDx3sG4NK8ZMEo78+gzlcgzaBSJoa2uXQ
// nq7cgrqfn0ig2rDxNBBwLzdBjMmAiiCL/X5BEZ5uK/RRTRbmKIH72rGXY3ApMfbn
// 1p+Mz3umCTcZdv5QJQIDAQAB
// -----END PUBLIC KEY-----',
                'notify_url' => 'https://www.lotteup.com/api/transaction/unifiedWithdrawalCallback',
                'enabled' => true,
            ],
        ],
        
        // 支持的国家（目前只支持印尼）
        'countries' => [
            'ID' => [
                'name' => '印尼',
                'name_en' => 'Indonesia',
                'currency' => 'IDR',
                'min_amount' => 50000,
                'max_amount' => ********,
                'enabled' => true,
            ],
        ],

        // JayaPay支持的支付类型
        'pay_types' => [
            // 只启用需要的支付方式
            'QRIS' => ['name' => 'QRIS', 'type' => 'qris', 'enabled' => true, 'requires_bank_code' => false, 'min_amount' => 10000, 'max_amount' => ********],
            'BNI' => ['name' => 'BNI', 'type' => 'bank', 'enabled' => true, 'requires_bank_code' => true, 'min_amount' => 10000, 'max_amount' => ********],
            'BRI' => ['name' => 'BRI', 'type' => 'bank', 'enabled' => true, 'requires_bank_code' => true, 'min_amount' => 10000, 'max_amount' => ********],
            'CIMB' => ['name' => 'CIMB', 'type' => 'bank', 'enabled' => true, 'requires_bank_code' => true, 'min_amount' => 10000, 'max_amount' => ********],
            'MANDIRI' => ['name' => 'MANDIRI', 'type' => 'bank', 'enabled' => true, 'requires_bank_code' => true, 'min_amount' => 10000, 'max_amount' => ********],
            'PERMATA' => ['name' => 'PERMATA', 'type' => 'bank', 'enabled' => true, 'requires_bank_code' => true, 'min_amount' => 10000, 'max_amount' => ********],

            // 禁用其他支付方式
            'BCA' => ['name' => 'Bank Central Asia', 'type' => 'bank', 'enabled' => false, 'requires_bank_code' => true],
            'MAYBANK' => ['name' => 'Bank Maybank', 'type' => 'bank', 'enabled' => false, 'requires_bank_code' => true],
            'DANAMON' => ['name' => 'Bank Danamon', 'type' => 'bank', 'enabled' => false, 'requires_bank_code' => true],
            'BSI' => ['name' => 'Bank Syariah Indonesia', 'type' => 'bank', 'enabled' => false, 'requires_bank_code' => true],
            'BNC' => ['name' => 'Bank Yudha Bhakti', 'type' => 'bank', 'enabled' => false, 'requires_bank_code' => true],
            'OVO' => ['name' => 'OVO', 'type' => 'wallet', 'enabled' => false, 'requires_bank_code' => false],
            'DANA' => ['name' => 'DANA', 'type' => 'wallet', 'enabled' => false, 'requires_bank_code' => false],
            'DANA_QRIS' => ['name' => 'DANA QRIS', 'type' => 'qris', 'enabled' => false, 'requires_bank_code' => false],
            'LINKAJA' => ['name' => 'LinkAja', 'type' => 'wallet', 'enabled' => false, 'requires_bank_code' => false],
            'SHOPEEPAY' => ['name' => 'ShopeePay', 'type' => 'wallet', 'enabled' => false, 'requires_bank_code' => false],
            'GOPAY_QRIS' => ['name' => 'GoPay QRIS', 'type' => 'qris', 'enabled' => false, 'requires_bank_code' => false],
            'ALFAMART' => ['name' => 'Alfamart', 'type' => 'offline', 'enabled' => false, 'requires_bank_code' => false],
            'TRANSFER_BCA' => ['name' => 'Transfer BCA', 'type' => 'transfer', 'enabled' => false, 'requires_bank_code' => true],
            'TRANSFER_DANA' => ['name' => 'Transfer DANA', 'type' => 'transfer', 'enabled' => false, 'requires_bank_code' => false],
        ],
        
        // 支持的支付方式（基于JayaPay文档）
        'payment_methods' => [
            // 银行转账
            'TRANSFER_BCA' => ['name' => 'BCA银行转账', 'type' => 'bank_transfer', 'enabled' => true],
            'TRANSFER_DANA' => ['name' => 'DANA转账', 'type' => 'bank_transfer', 'enabled' => true],

            // 银行支付
            'BCA' => ['name' => 'Bank Central Asia(BCA)', 'type' => 'online_banking', 'enabled' => true],
            'MANDIRI' => ['name' => 'Bank Mandiri', 'type' => 'online_banking', 'enabled' => true],
            'BNI' => ['name' => 'Bank Negara Indonesia(BNI)', 'type' => 'online_banking', 'enabled' => true],
            'BRI' => ['name' => 'Bank Rakyat Indonesia(BRI)', 'type' => 'online_banking', 'enabled' => true],
            'PERMATA' => ['name' => 'Bank Permata', 'type' => 'online_banking', 'enabled' => true],
            'CIMB' => ['name' => 'Bank CIMB Niaga', 'type' => 'online_banking', 'enabled' => true],
            'MAYBANK' => ['name' => 'Bank Maybank', 'type' => 'online_banking', 'enabled' => true],
            'DANAMON' => ['name' => 'Bank Danamon', 'type' => 'online_banking', 'enabled' => true],
            'BSI' => ['name' => 'Bank Syariah Indonesia(BSI)', 'type' => 'online_banking', 'enabled' => true],
            'BNC' => ['name' => 'Neo Commerce/Bank Yudha Bhakti(BNC)', 'type' => 'online_banking', 'enabled' => true],

            // 电子钱包
            'OVO' => ['name' => 'OVO', 'type' => 'e_wallet', 'enabled' => true],
            'DANA' => ['name' => 'DANA', 'type' => 'e_wallet', 'enabled' => true],
            'DANA_QRIS' => ['name' => 'DANA QRIS', 'type' => 'qr_code', 'enabled' => true],
            'LINKAJA' => ['name' => 'LINKAJA', 'type' => 'e_wallet', 'enabled' => true],
            'SHOPEEPAY' => ['name' => 'SHOPEEPAY', 'type' => 'e_wallet', 'enabled' => true],
            'GOPAY_QRIS' => ['name' => 'GOPAY QRIS', 'type' => 'qr_code', 'enabled' => true],

            // 二维码支付
            'QRIS' => ['name' => 'QRIS', 'type' => 'qr_code', 'enabled' => true],

            // 便利店支付
            'ALFAMART' => ['name' => 'ALFAMART', 'type' => 'convenience_store', 'enabled' => true],
        ],

        // JayaPay支持的银行编码（用于代付）- 完整版本
        'supported_banks' => [
            // 主流银行
            '014' => ['name' => 'Bank Central Asia(BCA)', 'name_id' => 'Bank Central Asia', 'enabled' => true],
            '008' => ['name' => 'Bank Mandiri', 'name_id' => 'Bank Mandiri', 'enabled' => true],
            '009' => ['name' => 'Bank Negara Indonesia(BNI)', 'name_id' => 'Bank Negara Indonesia', 'enabled' => true],
            '002' => ['name' => 'Bank Rakyat Indonesia(BRI)', 'name_id' => 'Bank Rakyat Indonesia', 'enabled' => true],
            '013' => ['name' => 'Bank Permata', 'name_id' => 'Bank Permata', 'enabled' => true],
            '022' => ['name' => 'Bank CIMB Niaga', 'name_id' => 'Bank CIMB Niaga', 'enabled' => true],
            '016' => ['name' => 'Bank Maybank', 'name_id' => 'Bank Maybank', 'enabled' => true],
            '011' => ['name' => 'Bank Danamon', 'name_id' => 'Bank Danamon', 'enabled' => true],
            '4510' => ['name' => 'Bank Syariah Indonesia(BSI)', 'name_id' => 'Bank Syariah Indonesia', 'enabled' => true],
            '490' => ['name' => 'Neo Commerce/Bank Yudha Bhakti(BNC)', 'name_id' => 'Bank Yudha Bhakti', 'enabled' => true],

            // 其他银行
            '200' => ['name' => 'Bank Tabungan Negara (BTN)', 'name_id' => 'Bank Tabungan Negara', 'enabled' => true],
            '213' => ['name' => 'Bank BTPN', 'name_id' => 'Bank BTPN', 'enabled' => true],
            '441' => ['name' => 'Wokee/Bukopin', 'name_id' => 'Bank Bukopin', 'enabled' => true],
            '031' => ['name' => 'Citibank', 'name_id' => 'Citibank', 'enabled' => true],
            '041' => ['name' => 'HSBC', 'name_id' => 'HSBC', 'enabled' => true],
            '050' => ['name' => 'Standard Chartered Bank', 'name_id' => 'Standard Chartered', 'enabled' => true],
            '023' => ['name' => 'TMRW/Bank UOB Indonesia', 'name_id' => 'Bank UOB Indonesia', 'enabled' => true],

            // 地方银行（BPD）
            '110' => ['name' => 'Bank Jawa Barat(BJB)', 'name_id' => 'Bank Jawa Barat', 'enabled' => true],
            '111' => ['name' => 'Bank DKI', 'name_id' => 'Bank DKI', 'enabled' => true],
            '113' => ['name' => 'Bank Jateng', 'name_id' => 'Bank Jawa Tengah', 'enabled' => true],
            '114' => ['name' => 'Bank Jatim', 'name_id' => 'Bank Jawa Timur', 'enabled' => true],
            '117' => ['name' => 'Bank Sumut', 'name_id' => 'Bank Sumatera Utara', 'enabled' => true],
            '118' => ['name' => 'BPD Sumatera Barat', 'name_id' => 'Bank Sumatera Barat', 'enabled' => true],
            '115' => ['name' => 'Bank Jambi', 'name_id' => 'Bank Jambi', 'enabled' => true],
            '120' => ['name' => 'BPD Sumsel Babel', 'name_id' => 'Bank Sumatera Selatan', 'enabled' => true],
            '123' => ['name' => 'BPD Kalimantan Barat/Kalbar', 'name_id' => 'Bank Kalimantan Barat', 'enabled' => true],
            '122' => ['name' => 'BPD Kalimantan Selatan/Kalsel', 'name_id' => 'Bank Kalimantan Selatan', 'enabled' => true],
            '125' => ['name' => 'BPD Kalimantan Tengah (Kalteng)', 'name_id' => 'Bank Kalimantan Tengah', 'enabled' => true],
            '129' => ['name' => 'BPD Bali', 'name_id' => 'Bank Bali', 'enabled' => true],
            '127' => ['name' => 'BPD Sulawesi Utara(SulutGo)', 'name_id' => 'Bank Sulawesi Utara', 'enabled' => true],
            '126' => ['name' => 'Bank Sulselbar', 'name_id' => 'Bank Sulawesi Selatan', 'enabled' => true],

            // 电子钱包
            '10001' => ['name' => 'OVO', 'name_id' => 'OVO', 'enabled' => true],
            '10002' => ['name' => 'DANA', 'name_id' => 'DANA', 'enabled' => true],
            '10003' => ['name' => 'GOPAY', 'name_id' => 'GOPAY', 'enabled' => true],
            '10009' => ['name' => 'LINKAJA', 'name_id' => 'LINKAJA', 'enabled' => true],
            '10008' => ['name' => 'SHOPEEPAY', 'name_id' => 'SHOPEEPAY', 'enabled' => true],
        ],
        
        // 订单状态映射（代收）
        'cash_in_status' => [
            'INIT_ORDER' => ['code' => 0, 'name' => '订单初始化', 'desc' => '订单已创建，等待支付'],
            'NO_PAY' => ['code' => 0, 'name' => '未支付', 'desc' => '订单未支付'],
            'SUCCESS' => ['code' => 1, 'name' => '支付成功', 'desc' => '支付已完成'],
            'PAY_CANCEL' => ['code' => 2, 'name' => '支付取消', 'desc' => '支付已取消'],
            'PAY_ERROR' => ['code' => 2, 'name' => '支付失败', 'desc' => '支付处理失败'],
        ],

        // 订单状态映射（代付）
        'cash_out_status' => [
            0 => ['name' => '待处理', 'desc' => '代付订单已提交，等待处理'],
            1 => ['name' => '已受理', 'desc' => '代付订单已受理，处理中'],
            2 => ['name' => '代付成功', 'desc' => '代付已完成'],
            4 => ['name' => '代付失败', 'desc' => '代付处理失败'],
            5 => ['name' => '银行代付中', 'desc' => '银行正在处理代付'],
        ],

        // 手续费类型
        'fee_types' => [
            '0' => '订单内扣除',
            '1' => '手续费另计',
        ],

        // 默认配置
        'defaults' => [
            'order_type' => '0',  // 法币交易
            'fee_type' => '0',    // 订单内扣除
            'expiry_period' => 1440, // 订单过期时间（分钟）
            'timeout' => 30,      // HTTP请求超时时间（秒）
        ],

        // 日志配置
        'log' => [
            'enabled' => true,
            'level' => 'info',
            'path' => 'jaya_pay'
        ],

        // 多语言支持
        'languages' => [
            'zh' => '中文',
            'en' => 'English',
            'id' => 'Bahasa Indonesia',
        ],

        // 错误码映射
        'error_codes' => [
            'INVALID_MERCHANT' => '无效商户',
            'INVALID_SIGN' => '签名验证失败',
            'INVALID_AMOUNT' => '金额无效',
            'INVALID_ORDER' => '订单无效',
            'SYSTEM_ERROR' => '系统错误',
            'NETWORK_ERROR' => '网络错误',
        ],

        // 回调IP白名单（根据JayaPay提供的IP配置）
        'notify_ips' => [
            // 这里需要根据JayaPay实际提供的回调IP进行配置
            // '123.456.789.0',
        ],

        // 重要提示和说明
        'setup_notes' => [
            'merchant_code' => '需要在JayaPay商户后台获取',
            'keys' => '需要生成RSA密钥对并在商户后台配置公钥',
            'platform_public_key' => '需要联系JayaPay获取平台公钥',
            'notify_ips' => '需要联系JayaPay获取回调IP白名单',
            'notify_url' => '需要配置为实际的域名地址',
        ],
    ],

    // 银行编码转换映射表（用于不同渠道间的编码转换）
    'bank_code_mapping' => [
        // 银行名称 => [bank_id, JayaPay编码, WatchPay编码]
        'Bank Central Asia' => ['bank_id' => 1, 'jayapay' => '014', 'watchpay' => 'BCA'],
        'Bank Mandiri' => ['bank_id' => 2, 'jayapay' => '008', 'watchpay' => 'MANDIRI'],
        'Bank Negara Indonesia' => ['bank_id' => 3, 'jayapay' => '009', 'watchpay' => 'BNI'],
        'Bank Rakyat Indonesia' => ['bank_id' => 4, 'jayapay' => '002', 'watchpay' => 'BRI'],
        'Bank Permata' => ['bank_id' => 5, 'jayapay' => '013', 'watchpay' => 'PERMATA'],
        'Bank CIMB Niaga' => ['bank_id' => 6, 'jayapay' => '022', 'watchpay' => 'CIMB'],
        'Bank Maybank' => ['bank_id' => 7, 'jayapay' => '016', 'watchpay' => 'MAYBANK'],
        'Bank Danamon' => ['bank_id' => 8, 'jayapay' => '011', 'watchpay' => 'DANAMON'],
        'Bank Syariah Indonesia' => ['bank_id' => 9, 'jayapay' => '4510', 'watchpay' => 'BSI'],
        'Bank Yudha Bhakti' => ['bank_id' => 10, 'jayapay' => '490', 'watchpay' => 'BNC'],
        'Bank Tabungan Negara' => ['bank_id' => 11, 'jayapay' => '200', 'watchpay' => 'BTN'],
        'Bank BTPN' => ['bank_id' => 12, 'jayapay' => '213', 'watchpay' => 'BTPN'],
        'Bank Bukopin' => ['bank_id' => 13, 'jayapay' => '441', 'watchpay' => 'BUKOPIN'],
        'Citibank' => ['bank_id' => 14, 'jayapay' => '031', 'watchpay' => 'CITIBANK'],

        // 添加简化的银行编码映射（前端可以直接传递编码）
        'BNI' => ['bank_id' => 3, 'jayapay' => '009', 'watchpay' => 'BNI'],
        'BRI' => ['bank_id' => 4, 'jayapay' => '002', 'watchpay' => 'BRI'],
        'CIMB' => ['bank_id' => 6, 'jayapay' => '022', 'watchpay' => 'CIMB'],
        'MANDIRI' => ['bank_id' => 2, 'jayapay' => '008', 'watchpay' => 'MANDIRI'],
        'PERMATA' => ['bank_id' => 5, 'jayapay' => '013', 'watchpay' => 'PERMATA'],
        'HSBC' => ['bank_id' => 15, 'jayapay' => '041', 'watchpay' => 'HSBC'],
        'Standard Chartered' => ['bank_id' => 16, 'jayapay' => '050', 'watchpay' => 'STANDARD_CHARTERED'],
        'Bank UOB Indonesia' => ['bank_id' => 17, 'jayapay' => '023', 'watchpay' => 'UOB'],
        'OVO' => ['bank_id' => 18, 'jayapay' => '10001', 'watchpay' => 'OVO'],
        'DANA' => ['bank_id' => 19, 'jayapay' => '10002', 'watchpay' => 'DANA'],
        'GOPAY' => ['bank_id' => 20, 'jayapay' => '10003', 'watchpay' => 'GOPAY'],
    ],


];
